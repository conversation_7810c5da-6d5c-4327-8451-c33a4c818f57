-- Sreekar Publishers Initial Reference Data
-- Insert basic reference data for the educational materials catalog

-- ============================================================================
-- GRADE LEVELS (6th to 10th Grade)
-- ============================================================================

INSERT OR IGNORE INTO grade_levels (grade_number, grade_name, display_order) VALUES
(6, '6th Grade', 1),
(7, '7th Grade', 2),
(8, '8th Grade', 3),
(9, '9th Grade', 4),
(10, '10th Grade', 5);

-- ============================================================================
-- MEDIUM TYPES (Telugu and English)
-- ============================================================================

INSERT OR IGNORE INTO medium_types (medium_code, medium_name, display_order) VALUES
('TEL', 'Telugu Medium', 1),
('ENG', 'English Medium', 2);

-- ============================================================================
-- SUBJECTS/CATEGORIES
-- ============================================================================

INSERT OR IGNORE INTO subjects (subject_code, subject_name, description, icon, color, display_order) VALUES
-- Core Academic Subjects
('MATH', 'Mathematics', 'Mathematical concepts, problem solving, and numerical skills', '🔢', '#FF6B35', 1),
('SCI', 'Science', 'Physics, Chemistry, Biology and General Science', '🔬', '#4ECDC4', 2),
('SOC', 'Social Studies', 'History, Geography, Civics and Social Sciences', '🌍', '#45B7D1', 3),

-- Language Subjects
('TEL', 'Telugu', 'Telugu language, literature and grammar', '📚', '#96CEB4', 4),
('ENG', 'English', 'English language, literature and communication skills', '📖', '#FFEAA7', 5),
('HIN', 'Hindi', 'Hindi language and literature', '📝', '#DDA0DD', 6),

-- Additional Subjects
('EVS', 'Environmental Studies', 'Environmental awareness and ecological studies', '🌱', '#98D8C8', 7),
('COMP', 'Computer Science', 'Basic computer skills and programming concepts', '💻', '#74B9FF', 8),
('ART', 'Arts & Crafts', 'Creative arts, drawing and craft activities', '🎨', '#FD79A8', 9),
('PE', 'Physical Education', 'Sports, fitness and physical activities', '⚽', '#FDCB6E', 10);

-- ============================================================================
-- BOOK SERIES/COLLECTIONS
-- ============================================================================

INSERT OR IGNORE INTO book_series (series_code, series_name, description) VALUES
-- Academic Series
('SREEKAR_MAIN', 'Sreekar Main Series', 'Primary textbook series covering all subjects'),
('SREEKAR_GUIDE', 'Sreekar Study Guides', 'Comprehensive study guides and reference materials'),
('SREEKAR_PRACTICE', 'Sreekar Practice Books', 'Exercise books and practice materials'),
('SREEKAR_EXAM', 'Sreekar Exam Preparation', 'Exam-focused preparation materials'),

-- Specialized Series
('SREEKAR_WORKBOOK', 'Sreekar Workbooks', 'Interactive workbooks with exercises'),
('SREEKAR_REFERENCE', 'Sreekar Reference', 'Quick reference and formula books'),
('SREEKAR_ACTIVITY', 'Sreekar Activity Books', 'Hands-on learning and activity books'),
('SREEKAR_ASSESSMENT', 'Sreekar Assessment', 'Test papers and assessment materials');



-- ============================================================================
-- SAMPLE TAGS FOR FLEXIBLE CATEGORIZATION
-- ============================================================================

INSERT OR IGNORE INTO tags (tag_name, tag_slug, description) VALUES
-- Academic Level Tags
('Beginner', 'beginner', 'Suitable for students new to the subject'),
('Intermediate', 'intermediate', 'For students with basic understanding'),
('Advanced', 'advanced', 'For students seeking deeper knowledge'),

-- Content Type Tags
('Textbook', 'textbook', 'Primary learning material'),
('Workbook', 'workbook', 'Practice and exercise book'),
('Reference', 'reference', 'Quick reference and lookup material'),
('Guide', 'guide', 'Study guide and supplementary material'),

-- Exam Preparation Tags
('Board Exam', 'board-exam', 'Preparation for board examinations'),
('Competitive Exam', 'competitive-exam', 'For competitive examination preparation'),
('Practice Tests', 'practice-tests', 'Mock tests and practice papers'),

-- Learning Style Tags
('Visual Learning', 'visual-learning', 'Books with diagrams, charts and visual aids'),
('Problem Solving', 'problem-solving', 'Focus on problem-solving techniques'),
('Conceptual', 'conceptual', 'Emphasis on understanding concepts'),
('Practical', 'practical', 'Hands-on and practical approach'),

-- Special Features Tags
('Illustrated', 'illustrated', 'Books with rich illustrations'),
('Bilingual', 'bilingual', 'Content available in multiple languages'),
('Updated Syllabus', 'updated-syllabus', 'Based on latest curriculum'),
('Previous Years', 'previous-years', 'Contains previous year questions');

-- ============================================================================
-- SAMPLE BOOKS DATA
-- ============================================================================

-- Mathematics Books
INSERT OR IGNORE INTO books (
    title, subtitle, description, isbn, url_slug,
    subject_id, grade_level_id, medium_type_id, series_id,
    price, old_price, page_count, primary_image,
    is_featured, is_new_release, stock_quantity,
    meta_title, meta_description, keywords,
    publication_date, edition_number
) VALUES
-- 6th Grade Mathematics
(
    'Mathematics Textbook', 'Grade 6 - Telugu Medium',
    'Comprehensive mathematics textbook covering all topics for 6th grade students in Telugu medium',
    '978-81-123456-01-1', 'mathematics-textbook-grade-6-telugu',
    (SELECT id FROM subjects WHERE subject_code = 'MATH'),
    (SELECT id FROM grade_levels WHERE grade_number = 6),
    (SELECT id FROM medium_types WHERE medium_code = 'TEL'),
    (SELECT id FROM book_series WHERE series_code = 'SREEKAR_MAIN'),
    299.00, 349.00, 256, '/images/books/math-6-tel-cover.jpg',
    1, 1, 150,
    'Mathematics Textbook Grade 6 Telugu Medium - Sreekar Publishers',
    'Best mathematics textbook for 6th grade Telugu medium students. Comprehensive coverage of all topics.',
    'mathematics, 6th grade, telugu medium, textbook, sreekar publishers',
    '2024-01-15', 3
),

-- 7th Grade Science
(
    'Science Textbook', 'Grade 7 - English Medium',
    'Complete science textbook covering Physics, Chemistry and Biology for 7th grade English medium students',
    '978-81-123456-02-2', 'science-textbook-grade-7-english',
    (SELECT id FROM subjects WHERE subject_code = 'SCI'),
    (SELECT id FROM grade_levels WHERE grade_number = 7),
    (SELECT id FROM medium_types WHERE medium_code = 'ENG'),
    (SELECT id FROM book_series WHERE series_code = 'SREEKAR_MAIN'),
    349.00, 399.00, 312, '/images/books/science-7-eng-cover.jpg',
    1, 0, 200,
    'Science Textbook Grade 7 English Medium - Sreekar Publishers',
    'Comprehensive science textbook for 7th grade English medium with practical examples.',
    'science, 7th grade, english medium, physics, chemistry, biology',
    '2024-02-01', 2
),

-- 8th Grade Social Studies
(
    'Social Studies', 'Grade 8 - Telugu Medium',
    'Comprehensive social studies covering History, Geography and Civics for 8th grade Telugu medium',
    '978-81-123456-03-3', 'social-studies-grade-8-telugu',
    (SELECT id FROM subjects WHERE subject_code = 'SOC'),
    (SELECT id FROM grade_levels WHERE grade_number = 8),
    (SELECT id FROM medium_types WHERE medium_code = 'TEL'),
    (SELECT id FROM book_series WHERE series_code = 'SREEKAR_MAIN'),
    279.00, 319.00, 288, '/images/books/social-8-tel-cover.jpg',
    0, 1, 120,
    'Social Studies Grade 8 Telugu Medium - History Geography Civics',
    'Complete social studies textbook for 8th grade Telugu medium students.',
    'social studies, 8th grade, telugu medium, history, geography, civics',
    '2024-01-20', 1
);



-- ============================================================================
-- SAMPLE BOOK TAGS ASSOCIATIONS
-- ============================================================================

INSERT OR IGNORE INTO book_tags (book_id, tag_id) VALUES
-- Mathematics Grade 6 Telugu Tags
(
    (SELECT id FROM books WHERE url_slug = 'mathematics-textbook-grade-6-telugu'),
    (SELECT id FROM tags WHERE tag_slug = 'textbook')
),
(
    (SELECT id FROM books WHERE url_slug = 'mathematics-textbook-grade-6-telugu'),
    (SELECT id FROM tags WHERE tag_slug = 'updated-syllabus')
),
(
    (SELECT id FROM books WHERE url_slug = 'mathematics-textbook-grade-6-telugu'),
    (SELECT id FROM tags WHERE tag_slug = 'illustrated')
),

-- Science Grade 7 English Tags
(
    (SELECT id FROM books WHERE url_slug = 'science-textbook-grade-7-english'),
    (SELECT id FROM tags WHERE tag_slug = 'textbook')
),
(
    (SELECT id FROM books WHERE url_slug = 'science-textbook-grade-7-english'),
    (SELECT id FROM tags WHERE tag_slug = 'practical')
),
(
    (SELECT id FROM books WHERE url_slug = 'science-textbook-grade-7-english'),
    (SELECT id FROM tags WHERE tag_slug = 'visual-learning')
),

-- Social Studies Grade 8 Telugu Tags
(
    (SELECT id FROM books WHERE url_slug = 'social-studies-grade-8-telugu'),
    (SELECT id FROM tags WHERE tag_slug = 'textbook')
),
(
    (SELECT id FROM books WHERE url_slug = 'social-studies-grade-8-telugu'),
    (SELECT id FROM tags WHERE tag_slug = 'conceptual')
),
(
    (SELECT id FROM books WHERE url_slug = 'social-studies-grade-8-telugu'),
    (SELECT id FROM tags WHERE tag_slug = 'illustrated')
);
