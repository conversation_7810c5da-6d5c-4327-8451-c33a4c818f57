# Sreekar Publishers Database Schema Summary

## Overview
This D1 SQLite database schema is specifically designed for Sreekar Publishers, an educational publisher specializing in materials for students in grades 6-10, supporting both Telugu and English medium instruction.

## Key Design Principles
- **Single Publisher Focus**: Designed for Sreekar Publishers only (no multi-publisher support)
- **Educational Domain**: Optimized for academic books and materials
- **Grade-Specific**: Supports grades 6-10 only
- **Bilingual Support**: Telugu and English medium materials
- **Simplified Structure**: No complex rating systems or multiple authors per book
- **Performance Optimized**: Comprehensive indexing for fast queries

## Core Tables Structure

### Reference Tables
```
grade_levels (6th-10th grade)
├── grade_number (6-10)
├── grade_name ("6th Grade", etc.)
└── display_order

medium_types (Telugu/English)
├── medium_code ("TEL", "ENG")
├── medium_name ("Telugu Medium", etc.)
└── display_order

subjects (Academic subjects)
├── subject_code ("MATH", "SCI", "SOC", etc.)
├── subject_name ("Mathematics", etc.)
├── icon (for UI)
├── color (for UI)
└── display_order

book_series (Collections)
├── series_code ("SREEKAR_MAIN", etc.)
├── series_name ("Sreekar Main Series", etc.)
└── description
```

### Main Entity Table
```
books (Educational materials)
├── Basic Info
│   ├── title, subtitle, description
│   ├── isbn, url_slug
│   └── primary_image
├── Classification
│   ├── subject_id → subjects
│   ├── grade_level_id → grade_levels
│   ├── medium_type_id → medium_types
│   └── series_id → book_series
├── Pricing
│   ├── price, old_price, cost_price
├── Physical Properties
│   ├── page_count, weight_grams
│   ├── dimensions, binding_type
├── Status Flags
│   ├── is_featured, is_new_release
│   ├── is_bestseller, is_on_sale
│   └── is_available
├── Stock Management
│   ├── stock_quantity
│   └── low_stock_threshold
├── SEO
│   ├── meta_title, meta_description
│   └── keywords
└── Publication
    ├── publication_date
    └── edition_number
```

### Association Tables
```
tags (Flexible categorization)
├── tag_name, tag_slug
└── description

book_tags (Many-to-many: books ↔ tags)
├── book_id → books
└── tag_id → tags
```

## Sample Data Categories

### Subjects Included
- **Core Academic**: Mathematics, Science, Social Studies
- **Languages**: Telugu, English, Hindi
- **Additional**: Environmental Studies, Computer Science, Arts & Crafts, Physical Education

### Book Series Types
- **SREEKAR_MAIN**: Primary textbook series
- **SREEKAR_GUIDE**: Study guides and reference materials
- **SREEKAR_PRACTICE**: Exercise books and practice materials
- **SREEKAR_EXAM**: Exam preparation materials
- **SREEKAR_WORKBOOK**: Interactive workbooks
- **SREEKAR_REFERENCE**: Quick reference books
- **SREEKAR_ACTIVITY**: Hands-on learning materials
- **SREEKAR_ASSESSMENT**: Test papers and assessments

### Tag Categories
- **Academic Level**: Beginner, Intermediate, Advanced
- **Content Type**: Textbook, Workbook, Reference, Guide
- **Exam Preparation**: Board Exam, Competitive Exam, Practice Tests
- **Learning Style**: Visual Learning, Problem Solving, Conceptual, Practical
- **Special Features**: Illustrated, Bilingual, Updated Syllabus, Previous Years

## Performance Features

### Optimized Indexes
- **Primary Lookups**: subject + grade + medium combinations
- **Status Filters**: Featured, new releases, bestsellers, on sale
- **Sorting**: Price, publication date, creation date
- **Stock Management**: Low stock detection
- **SEO**: URL slug and ISBN lookups

### Useful Views
- **v_books_complete**: Complete book information with all related data
- **v_books_by_grade_medium**: Statistical view for analytics
- **v_stock_status**: Stock management with status categorization

### Automated Features
- **Timestamp Updates**: Automatic `updated_at` field maintenance
- **Data Integrity**: Foreign key constraints and check constraints

## Common Query Patterns

### Catalog Browsing
```sql
-- Books for specific grade and medium
SELECT * FROM v_books_complete 
WHERE grade_number = 8 AND medium_code = 'TEL' AND is_available = 1;

-- Featured books
SELECT * FROM v_books_complete 
WHERE is_featured = 1 AND is_available = 1;
```

### Inventory Management
```sql
-- Low stock alerts
SELECT * FROM v_stock_status 
WHERE stock_status IN ('Out of Stock', 'Low Stock');

-- Books by series
SELECT * FROM v_books_complete 
WHERE series_name = 'Sreekar Main Series';
```

### Advanced Filtering
```sql
-- Books with specific tags
SELECT DISTINCT b.* FROM v_books_complete b
JOIN book_tags bt ON b.id = bt.book_id
JOIN tags t ON bt.tag_id = t.id
WHERE t.tag_slug = 'visual-learning' AND b.is_available = 1;
```

## File Structure
- **`sreekar_publishers_schema.sql`**: Complete database schema
- **`sreekar_publishers_initial_data.sql`**: Reference data and sample books
- **`README.md`**: Detailed documentation
- **`SCHEMA_SUMMARY.md`**: This summary document

## Implementation Notes
- Uses SQLite/D1 compatible syntax
- All tables use `IF NOT EXISTS` for safe deployment
- Comprehensive error handling with check constraints
- Optimized for Cloudflare D1 database platform
- Ready for production deployment

## Benefits for Sreekar Publishers
1. **Fast Catalog Browsing**: Optimized indexes for grade/medium/subject combinations
2. **Efficient Stock Management**: Built-in low stock detection and reporting
3. **SEO Friendly**: URL slugs and meta fields for online catalog
4. **Flexible Categorization**: Tag system for future expansion
5. **Data Integrity**: Comprehensive constraints and foreign keys
6. **Performance**: Views and indexes for common query patterns
7. **Scalability**: Designed to handle thousands of books efficiently

This schema provides a solid foundation for Sreekar Publishers' educational materials catalog while maintaining simplicity and performance.
