-- Sreekar Publishers D1 SQLite Database Schema
-- Educational Materials Catalog for Grades 6-10 (Telugu & English Medium)

-- ============================================================================
-- CORE REFERENCE TABLES
-- ============================================================================

-- Grade Levels (6th to 10th grade)
CREATE TABLE IF NOT EXISTS grade_levels (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    grade_number INTEGER NOT NULL UNIQUE CHECK (grade_number BETWEEN 6 AND 10),
    grade_name TEXT NOT NULL, -- '6th Grade', '7th Grade', etc.
    display_order INTEGER NOT NULL,
    is_active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Medium Types (Telugu/English)
CREATE TABLE IF NOT EXISTS medium_types (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    medium_code TEXT NOT NULL UNIQUE, -- 'TEL', 'ENG'
    medium_name TEXT NOT NULL, -- 'Telugu Medium', 'English Medium'
    display_order INTEGER NOT NULL,
    is_active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Subjects/Categories
CREATE TABLE IF NOT EXISTS subjects (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    subject_code TEXT NOT NULL UNIQUE, -- 'MATH', 'SCI', 'SOC', 'TEL', 'ENG', 'HIN'
    subject_name TEXT NOT NULL, -- 'Mathematics', 'Science', 'Social Studies', etc.
    description TEXT,
    icon TEXT, -- Icon class or emoji
    color TEXT, -- Hex color code for UI
    display_order INTEGER NOT NULL,
    is_active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Book Series/Collections
CREATE TABLE IF NOT EXISTS book_series (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    series_code TEXT NOT NULL UNIQUE,
    series_name TEXT NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Authors (even though single publisher, books may have different authors)
CREATE TABLE IF NOT EXISTS authors (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    author_name TEXT NOT NULL,
    author_bio TEXT,
    is_active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ============================================================================
-- MAIN BOOKS/PRODUCTS TABLE
-- ============================================================================

-- Educational Materials/Books
CREATE TABLE IF NOT EXISTS books (
    id INTEGER PRIMARY KEY AUTOINCREMENT,

    -- Basic Information
    title TEXT NOT NULL,
    subtitle TEXT,
    description TEXT,
    isbn TEXT UNIQUE, -- International Standard Book Number
    url_slug TEXT NOT NULL UNIQUE, -- For SEO-friendly URLs

    -- Classification
    subject_id INTEGER NOT NULL,
    grade_level_id INTEGER NOT NULL,
    medium_type_id INTEGER NOT NULL,
    series_id INTEGER,

    -- Pricing
    price DECIMAL(10, 2) NOT NULL CHECK (price >= 0),
    old_price DECIMAL(10, 2) CHECK (old_price >= price),
    cost_price DECIMAL(10, 2) CHECK (cost_price >= 0),

    -- Physical Properties
    page_count INTEGER CHECK (page_count > 0),
    weight_grams INTEGER CHECK (weight_grams > 0),
    dimensions TEXT, -- "Length x Width x Height in cm"
    binding_type TEXT DEFAULT 'Paperback', -- 'Paperback', 'Hardcover'

    -- Images
    primary_image TEXT NOT NULL, -- Main book cover image URL

    -- Status Flags
    is_featured BOOLEAN DEFAULT 0,
    is_new_release BOOLEAN DEFAULT 0,
    is_bestseller BOOLEAN DEFAULT 0,
    is_on_sale BOOLEAN DEFAULT 0,
    is_available BOOLEAN DEFAULT 1,

    -- Stock Management
    stock_quantity INTEGER DEFAULT 0 CHECK (stock_quantity >= 0),
    low_stock_threshold INTEGER DEFAULT 10,

    -- SEO & Marketing
    meta_title TEXT,
    meta_description TEXT,
    keywords TEXT, -- Comma-separated keywords


    -- Publication Info
    publication_date DATE,
    edition_number INTEGER DEFAULT 1,

    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- Foreign Key Constraints
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE RESTRICT,
    FOREIGN KEY (grade_level_id) REFERENCES grade_levels(id) ON DELETE RESTRICT,
    FOREIGN KEY (medium_type_id) REFERENCES medium_types(id) ON DELETE RESTRICT,
    FOREIGN KEY (series_id) REFERENCES book_series(id) ON DELETE SET NULL
);

-- ============================================================================
-- SUPPORTING TABLES
-- ============================================================================

-- Book Authors (Many-to-Many relationship)
CREATE TABLE IF NOT EXISTS book_authors (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    book_id INTEGER NOT NULL,
    author_id INTEGER NOT NULL,
    author_role TEXT DEFAULT 'Author', -- 'Author', 'Co-Author', 'Editor', 'Contributor'
    display_order INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE,
    FOREIGN KEY (author_id) REFERENCES authors(id) ON DELETE CASCADE,
    UNIQUE(book_id, author_id, author_role)
);

-- Additional Book Images
CREATE TABLE IF NOT EXISTS book_images (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    book_id INTEGER NOT NULL,
    image_url TEXT NOT NULL,
    image_type TEXT DEFAULT 'gallery', -- 'cover', 'back', 'inside', 'gallery'
    alt_text TEXT,
    display_order INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE
);

-- Book Reviews
CREATE TABLE IF NOT EXISTS book_reviews (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    book_id INTEGER NOT NULL,
    reviewer_name TEXT NOT NULL,
    reviewer_email TEXT,
    rating INTEGER NOT NULL CHECK (rating BETWEEN 1 AND 5),
    review_title TEXT,
    review_text TEXT,
    is_verified_purchase BOOLEAN DEFAULT 0,
    is_approved BOOLEAN DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE
);

-- Book Tags (for flexible categorization)
CREATE TABLE IF NOT EXISTS tags (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    tag_name TEXT NOT NULL UNIQUE,
    tag_slug TEXT NOT NULL UNIQUE,
    description TEXT,
    is_active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Book Tags Association
CREATE TABLE IF NOT EXISTS book_tags (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    book_id INTEGER NOT NULL,
    tag_id INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE,
    FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE,
    UNIQUE(book_id, tag_id)
);

-- ============================================================================
-- INDEXES FOR PERFORMANCE
-- ============================================================================

-- Primary lookup indexes
CREATE INDEX IF NOT EXISTS idx_books_subject_grade_medium ON books(subject_id, grade_level_id, medium_type_id);
CREATE INDEX IF NOT EXISTS idx_books_grade_medium ON books(grade_level_id, medium_type_id);
CREATE INDEX IF NOT EXISTS idx_books_subject_medium ON books(subject_id, medium_type_id);
CREATE INDEX IF NOT EXISTS idx_books_url_slug ON books(url_slug);
CREATE INDEX IF NOT EXISTS idx_books_isbn ON books(isbn);

-- Status and feature indexes
CREATE INDEX IF NOT EXISTS idx_books_featured ON books(is_featured) WHERE is_featured = 1;
CREATE INDEX IF NOT EXISTS idx_books_new_release ON books(is_new_release) WHERE is_new_release = 1;
CREATE INDEX IF NOT EXISTS idx_books_bestseller ON books(is_bestseller) WHERE is_bestseller = 1;
CREATE INDEX IF NOT EXISTS idx_books_on_sale ON books(is_on_sale) WHERE is_on_sale = 1;
CREATE INDEX IF NOT EXISTS idx_books_available ON books(is_available) WHERE is_available = 1;

-- Sorting and filtering indexes
CREATE INDEX IF NOT EXISTS idx_books_price ON books(price);
CREATE INDEX IF NOT EXISTS idx_books_rating ON books(average_rating DESC);
CREATE INDEX IF NOT EXISTS idx_books_publication_date ON books(publication_date DESC);
CREATE INDEX IF NOT EXISTS idx_books_created_at ON books(created_at DESC);

-- Stock management indexes
CREATE INDEX IF NOT EXISTS idx_books_stock ON books(stock_quantity);
CREATE INDEX IF NOT EXISTS idx_books_low_stock ON books(stock_quantity, low_stock_threshold)
    WHERE stock_quantity <= low_stock_threshold;

-- Reference table indexes
CREATE INDEX IF NOT EXISTS idx_grade_levels_active ON grade_levels(is_active, display_order);
CREATE INDEX IF NOT EXISTS idx_medium_types_active ON medium_types(is_active, display_order);
CREATE INDEX IF NOT EXISTS idx_subjects_active ON subjects(is_active, display_order);
CREATE INDEX IF NOT EXISTS idx_book_series_active ON book_series(is_active);

-- Association table indexes
CREATE INDEX IF NOT EXISTS idx_book_authors_book ON book_authors(book_id);
CREATE INDEX IF NOT EXISTS idx_book_authors_author ON book_authors(author_id);
CREATE INDEX IF NOT EXISTS idx_book_images_book ON book_images(book_id, display_order);
CREATE INDEX IF NOT EXISTS idx_book_reviews_book ON book_reviews(book_id);
CREATE INDEX IF NOT EXISTS idx_book_reviews_approved ON book_reviews(is_approved) WHERE is_approved = 1;
CREATE INDEX IF NOT EXISTS idx_book_tags_book ON book_tags(book_id);
CREATE INDEX IF NOT EXISTS idx_book_tags_tag ON book_tags(tag_id);

-- ============================================================================
-- USEFUL VIEWS FOR COMMON QUERIES
-- ============================================================================

-- Complete book information view with all related data
CREATE VIEW IF NOT EXISTS v_books_complete AS
SELECT
    b.id,
    b.title,
    b.subtitle,
    b.description,
    b.isbn,
    b.url_slug,

    -- Classification
    s.subject_name,
    s.subject_code,
    s.icon as subject_icon,
    s.color as subject_color,
    gl.grade_name,
    gl.grade_number,
    mt.medium_name,
    mt.medium_code,
    bs.series_name,

    -- Pricing
    b.price,
    b.old_price,
    b.cost_price,

    -- Physical Properties
    b.page_count,
    b.weight_grams,
    b.dimensions,
    b.binding_type,

    -- Images
    b.primary_image,

    -- Status
    b.is_featured,
    b.is_new_release,
    b.is_bestseller,
    b.is_on_sale,
    b.is_available,

    -- Stock
    b.stock_quantity,
    b.low_stock_threshold,
    CASE
        WHEN b.stock_quantity <= b.low_stock_threshold THEN 1
        ELSE 0
    END as is_low_stock,

    -- Ratings
    b.average_rating,
    b.total_reviews,

    -- Publication
    b.publication_date,
    b.edition_number,

    -- Timestamps
    b.created_at,
    b.updated_at

FROM books b
LEFT JOIN subjects s ON b.subject_id = s.id
LEFT JOIN grade_levels gl ON b.grade_level_id = gl.id
LEFT JOIN medium_types mt ON b.medium_type_id = mt.id
LEFT JOIN book_series bs ON b.series_id = bs.id;

-- Books by grade and medium view
CREATE VIEW IF NOT EXISTS v_books_by_grade_medium AS
SELECT
    gl.grade_number,
    gl.grade_name,
    mt.medium_code,
    mt.medium_name,
    s.subject_name,
    COUNT(b.id) as book_count,
    AVG(b.price) as avg_price,
    MIN(b.price) as min_price,
    MAX(b.price) as max_price
FROM books b
JOIN subjects s ON b.subject_id = s.id
JOIN grade_levels gl ON b.grade_level_id = gl.id
JOIN medium_types mt ON b.medium_type_id = mt.id
WHERE b.is_available = 1
GROUP BY gl.grade_number, mt.medium_code, s.subject_name
ORDER BY gl.grade_number, mt.display_order, s.display_order;

-- Stock status view
CREATE VIEW IF NOT EXISTS v_stock_status AS
SELECT
    b.id,
    b.title,
    s.subject_name,
    gl.grade_name,
    mt.medium_name,
    b.stock_quantity,
    b.low_stock_threshold,
    CASE
        WHEN b.stock_quantity = 0 THEN 'Out of Stock'
        WHEN b.stock_quantity <= b.low_stock_threshold THEN 'Low Stock'
        ELSE 'In Stock'
    END as stock_status,
    b.price,
    b.is_available
FROM books b
JOIN subjects s ON b.subject_id = s.id
JOIN grade_levels gl ON b.grade_level_id = gl.id
JOIN medium_types mt ON b.medium_type_id = mt.id
ORDER BY
    CASE
        WHEN b.stock_quantity = 0 THEN 1
        WHEN b.stock_quantity <= b.low_stock_threshold THEN 2
        ELSE 3
    END,
    b.stock_quantity ASC;

-- ============================================================================
-- TRIGGERS FOR DATA INTEGRITY AND AUTOMATION
-- ============================================================================

-- Update timestamp trigger for books
CREATE TRIGGER IF NOT EXISTS tr_books_updated_at
    AFTER UPDATE ON books
    FOR EACH ROW
BEGIN
    UPDATE books SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- Update average rating when reviews are added/updated
CREATE TRIGGER IF NOT EXISTS tr_update_book_rating_after_review_insert
    AFTER INSERT ON book_reviews
    FOR EACH ROW
    WHEN NEW.is_approved = 1
BEGIN
    UPDATE books
    SET
        average_rating = (
            SELECT AVG(CAST(rating AS REAL))
            FROM book_reviews
            WHERE book_id = NEW.book_id AND is_approved = 1
        ),
        total_reviews = (
            SELECT COUNT(*)
            FROM book_reviews
            WHERE book_id = NEW.book_id AND is_approved = 1
        )
    WHERE id = NEW.book_id;
END;

-- Update average rating when reviews are updated
CREATE TRIGGER IF NOT EXISTS tr_update_book_rating_after_review_update
    AFTER UPDATE ON book_reviews
    FOR EACH ROW
    WHEN NEW.is_approved = 1 OR OLD.is_approved = 1
BEGIN
    UPDATE books
    SET
        average_rating = (
            SELECT COALESCE(AVG(CAST(rating AS REAL)), 0)
            FROM book_reviews
            WHERE book_id = NEW.book_id AND is_approved = 1
        ),
        total_reviews = (
            SELECT COUNT(*)
            FROM book_reviews
            WHERE book_id = NEW.book_id AND is_approved = 1
        )
    WHERE id = NEW.book_id;
END;

-- Update average rating when reviews are deleted
CREATE TRIGGER IF NOT EXISTS tr_update_book_rating_after_review_delete
    AFTER DELETE ON book_reviews
    FOR EACH ROW
    WHEN OLD.is_approved = 1
BEGIN
    UPDATE books
    SET
        average_rating = (
            SELECT COALESCE(AVG(CAST(rating AS REAL)), 0)
            FROM book_reviews
            WHERE book_id = OLD.book_id AND is_approved = 1
        ),
        total_reviews = (
            SELECT COUNT(*)
            FROM book_reviews
            WHERE book_id = OLD.book_id AND is_approved = 1
        )
    WHERE id = OLD.book_id;
END;
