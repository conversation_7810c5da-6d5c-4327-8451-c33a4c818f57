---
interface Props {
  title?: string;
  description?: string;
  showHeader?: boolean;
  showBackButton?: boolean;
  headerTitle?: string;
  showFooter?: boolean;
  image?: string;
  canonicalURL?: string;
  type?: 'website' | 'article' | 'product';
  keywords?: string;
  author?: string;
  publishDate?: Date;
  modifiedDate?: Date;
  schema?: any;
}

const {
  title = "Sreekar Publishers - Educational Materials",
  description = "Quality educational materials for students from 6th to 10th grade. Telugu and English Medium study resources, textbooks, and exam preparation materials.",
  showHeader = true,
  showBackButton = false,
  headerTitle = "", // Default is empty
  showFooter = true,
  image = "/images/highq-foods-social-image.jpg", // Default social image
  canonicalURL,
  type = "website",
  keywords = "educational materials, study resources, Telugu medium, English medium, 6th to 10th grade, textbooks, exam preparation, Sreekar Publishers, Eluru",
  author = "Sreekar Publishers Team",
  publishDate,
  modifiedDate,
  schema,
} = Astro.props;

// Import global styles
import "../styles/global.css";

// Safely generate URLs to avoid Invalid URL errors
const siteUrl = Astro.site ? Astro.site.toString() : 'https://sreekarpublishers.com/';
const baseUrl = siteUrl.endsWith('/') ? siteUrl : `${siteUrl}/`;
const currentPath = Astro.url.pathname;
const currentUrl = new URL(currentPath.startsWith('/') ? currentPath.slice(1) : currentPath, baseUrl);
const finalCanonicalURL = canonicalURL || currentUrl.href;

// Generate the absolute URL for the social image
let socialImageURL;
try {
  // Handle both absolute and relative image paths
  socialImageURL = image.startsWith('http') ? image : new URL(image.startsWith('/') ? image.slice(1) : image, baseUrl).href;
} catch (e) {
  // Fallback if URL creation fails
  socialImageURL = `${baseUrl}images/sreekar-publishers-social-image.png`;
}

// Schema.org JSON-LD
const defaultSchema = {
  "@context": "https://schema.org",
  "@type": "BookStore",
  "name": "Sreekar Publishers",
  "url": baseUrl,
  "logo": `${baseUrl}images/icons/icon-512x512.png`,
  "address": {
    "@type": "PostalAddress",
    "streetAddress": "Door No: 2-56, CHANIKYAPURI COLONY 2ND LINE, 34 MC DIVISION, NEAR D MART",
    "addressLocality": "ELURU URBAN",
    "addressRegion": "ELURU",
    "postalCode": "534002",
    "addressCountry": "IN"
  },
  "geo": {
    "@type": "GeoCoordinates",
    "latitude": "16.7107",
    "longitude": "81.1031"
  },
  "telephone": "+91-9876543210",
  "educationalUse": ["Study Materials", "Textbooks", "Reference Books", "Exam Preparation"],
  "priceRange": "₹₹",
  "openingHours": "Mo-Su 08:00-22:00",
  "sameAs": [
    "https://facebook.com/sreekarpublishers",
    "https://instagram.com/sreekarpublishers"
  ]
};

const jsonLD = schema || defaultSchema;
---

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" type="image/png" href="/images/icons/icon-192x192.png" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, viewport-fit=cover, maximum-scale=1.0, user-scalable=no"
    />

    <!-- Primary Meta Tags -->
    <title>{title}</title>
    <meta name="title" content={title} />
    <meta name="description" content={description} />
    <meta name="keywords" content={keywords} />
    <meta name="author" content={author} />
    <link rel="canonical" href={finalCanonicalURL} />

    {publishDate && <meta name="date" content={publishDate.toISOString()} />}
    {modifiedDate && <meta name="revised" content={modifiedDate.toISOString()} />}

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content={type} />
    <meta property="og:url" content={finalCanonicalURL} />
    <meta property="og:title" content={title} />
    <meta property="og:description" content={description} />
    <meta property="og:image" content={socialImageURL} />
    <meta property="og:site_name" content="Sreekar Publishers" />
    <meta property="og:locale" content="en_IN" />
    <link rel="sitemap" href="/sitemap-index.xml" />
    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:url" content={finalCanonicalURL} />
    <meta name="twitter:title" content={title} />
    <meta name="twitter:description" content={description} />
    <meta name="twitter:image" content={socialImageURL} />
    <meta name="twitter:creator" content="@sreekarpublishers" />

    <!-- iOS PWA Meta Tags -->
    <meta name="theme-color" content="#FF6B35" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="Sreekar Publishers" />

    <!-- Apple Touch Icons -->
    <link rel="apple-touch-icon" href="/images/icons/icon-192x192.png" />
    <link rel="apple-touch-icon" sizes="152x152" href="/images/icons/icon-192x192.png" />
    <link rel="apple-touch-icon" sizes="180x180" href="/images/icons/icon-192x192.png" />
    <link rel="apple-touch-icon" sizes="167x167" href="/images/icons/icon-192x192.png" />

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json" />

    <!-- Immediate Service Worker Registration -->
    <script is:inline>
      // Register Service Worker as early as possible
      if ('serviceWorker' in navigator) {
        navigator.serviceWorker.register('/sw.js')
          .then(registration => {
            console.log('Service Worker registered with scope:', registration.scope);
          })
          .catch(error => {
            console.error('Service Worker registration failed:', error);
          });
      }
    </script>

    <!-- Structured data -->
    <script type="application/ld+json" set:html={JSON.stringify(jsonLD)} />

    <!-- Preload Critical Resources -->
    <link rel="preload" as="font" href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" crossorigin />
    <link rel="preload" as="font" href="https://fonts.googleapis.com/icon?family=Material+Icons+Round" crossorigin />

    <!-- Google Fonts for better typography -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <!-- Material Icons for navigation -->
    <link
      href="https://fonts.googleapis.com/icon?family=Material+Icons+Round"
      rel="stylesheet"
    />
  </head>
  <body class="flex flex-col bg-gray-50">
    <!-- Status Bar Spacer for iOS devices -->
    <div id="status-bar-spacer" class="w-full bg-white z-50"></div>

    <!-- Enhanced Professional Educational Header -->
    {
      showHeader && (
        <header
          id="app-header"
          class="sticky top-0 z-30 bg-white/95 backdrop-blur-md flex items-center justify-between border-b border-blue-100 shadow-sm"
        >
          <div class="w-full max-w-7xl mx-auto flex items-center justify-between px-4 h-16">
            <div class="flex items-center flex-1">
              {showBackButton ? (
                <button
                  onclick="history.back()"
                  class="flex items-center justify-center -ml-2 p-3 rounded-xl hover:bg-blue-50 active:bg-blue-100 transition-all duration-200 group"
                  aria-label="Go back"
                >
                  <span class="material-icons-round text-blue-600 group-hover:text-blue-700">
                    arrow_back
                  </span>
                </button>
              ) : (
                <div class="app-logo flex items-center">
                  <div class="w-12 h-12 rounded-2xl flex items-center justify-center shadow-md overflow-hidden bg-gradient-to-br from-blue-500 to-blue-600 ring-2 ring-blue-100">
                    <img
                      src="/images/icons/icon-192x192.png"
                      alt="Sreekar Publishers Logo"
                      width="48"
                      height="48"
                      class="w-full h-full object-cover"
                    />
                  </div>
                  {headerTitle ? (
                    <h1 class="text-xl font-bold text-gray-800 ml-4 tracking-tight">
                      {headerTitle}
                    </h1>
                  ) : (
                    <div class="ml-4">
                      <h1 class="text-xl font-bold text-gray-800 leading-tight tracking-tight">
                        Sreekar Publishers
                      </h1>
                      <p class="text-sm text-blue-600 font-medium -mt-0.5">
                        Educational Excellence
                      </p>
                    </div>
                  )}
                </div>
              )}
            </div>

            <div class="flex items-center gap-3">
              <a
                href="/cart"
                class="p-3 rounded-xl hover:bg-blue-50 active:bg-blue-100 transition-all duration-200 inline-flex items-center justify-center relative group"
                aria-label="Cart"
              >
                <span class="material-icons-round text-blue-600 group-hover:text-blue-700">
                  shopping_bag
                </span>
                <span
                  id="cart-badge"
                  class="absolute -top-1 -right-1 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-full w-6 h-6 text-xs font-bold flex items-center justify-center border-2 border-white shadow-lg"
                  aria-hidden="true"
                >
                  0
                </span>
              </a>
            </div>
          </div>
        </header>
      )
    }

    <main class="flex-1 pb-20 pt-0">
      <slot />
    </main>

    <!-- Enhanced Professional Educational Bottom Navigation -->
    {
      showFooter && (
        <nav
          class="fixed bottom-0 left-0 right-0 bg-white/95 backdrop-blur-md flex justify-around items-center shadow-xl z-40 border-t border-blue-100"
          style="height: calc(68px + var(--safe-area-inset-bottom)); padding-bottom: var(--safe-area-inset-bottom);"
          aria-label="Main navigation"
        >
          <a
            href="/"
            class="nav-tab flex flex-col items-center justify-center h-full flex-1 relative pt-2 pb-1 group"
            data-target="home"
            aria-label="Home"
          >
            <div class="nav-icon-container rounded-2xl w-12 h-12 flex items-center justify-center mb-1 transition-all duration-200 group-hover:bg-blue-50 group-active:bg-blue-100">
              <span class="material-icons-round text-[22px] text-blue-600 group-hover:text-blue-700">home</span>
            </div>
            <span class="text-[11px] font-medium text-blue-600 group-hover:text-blue-700">Home</span>
          </a>
          <a
            href="/favorites"
            class="nav-tab flex flex-col items-center justify-center h-full flex-1 relative pt-2 pb-1 group"
            data-target="favorites"
            aria-label="Favorites"
          >
            <div class="nav-icon-container rounded-2xl w-12 h-12 flex items-center justify-center mb-1 transition-all duration-200 group-hover:bg-red-50 group-active:bg-red-100">
              <span class="material-icons-round text-[22px] text-gray-500 group-hover:text-red-500">favorite</span>
            </div>
            <span class="text-[11px] font-medium text-gray-500 group-hover:text-red-500">Favorites</span>
          </a>

          <a
            href="/orders"
            class="nav-tab flex flex-col items-center justify-center h-full flex-1 relative pt-2 pb-1 group"
            data-target="projects"
            aria-label="Orders"
          >
            <div class="nav-icon-container rounded-2xl w-12 h-12 flex items-center justify-center mb-1 transition-all duration-200 group-hover:bg-green-50 group-active:bg-green-100">
              <span class="material-icons-round text-[22px] text-gray-500 group-hover:text-green-600">receipt_long</span>
            </div>
            <span class="text-[11px] font-medium text-gray-500 group-hover:text-green-600">Orders</span>
          </a>
          <a
            href="/cart"
            class="nav-tab flex flex-col items-center justify-center h-full flex-1 relative pt-2 pb-1 group"
            data-target="cart"
            aria-label="Cart"
          >
            <div class="nav-icon-container rounded-2xl w-12 h-12 flex items-center justify-center mb-1 relative transition-all duration-200 group-hover:bg-orange-50 group-active:bg-orange-100">
              <span class="material-icons-round text-[22px] text-gray-500 group-hover:text-orange-600">
                shopping_cart
              </span>
              <span class="absolute -top-1 -right-1 bg-gradient-to-r from-orange-500 to-red-500 text-white rounded-full w-5 h-5 text-[10px] font-bold flex items-center justify-center border-2 border-white shadow-md">
                3
              </span>
            </div>
            <span class="text-[11px] font-medium text-gray-500 group-hover:text-orange-600">Cart</span>
          </a>
          <a
            href="/profile"
            class="nav-tab flex flex-col items-center justify-center h-full flex-1 relative pt-2 pb-1 group"
            data-target="profile"
            aria-label="Profile"
          >
            <div class="nav-icon-container rounded-2xl w-12 h-12 flex items-center justify-center mb-1 transition-all duration-200 group-hover:bg-purple-50 group-active:bg-purple-100">
              <span class="material-icons-round text-[22px] text-gray-500 group-hover:text-purple-600">person</span>
            </div>
            <span class="text-[11px] font-medium text-gray-500 group-hover:text-purple-600">Profile</span>
          </a>
        </nav>
      )
    }

    <!-- App Scripts -->
    <script is:inline src="/scripts/app.js"></script>
    <script is:inline src="/scripts/swipe.js"></script>
    <script is:inline src="/scripts/utils-initializer.js"></script>

    <!-- Initial Setup Script -->
    <script is:inline>
      // Initial setup on first page load
      document.addEventListener("DOMContentLoaded", () => {
        // Initialize status bar, nav tabs, and touch feedback
        // These will be reinitialized on page transitions by transition-handler.js
        if (typeof initStatusBar === 'function') initStatusBar();
        if (typeof initNavTabs === 'function') initNavTabs();
        if (typeof initTouchFeedback === 'function') initTouchFeedback();
      });
    </script>

    <!-- Add utility scripts -->
    <script src="/scripts/cart-utils.js" is:inline></script>
    <script src="/scripts/favorites-utils.js" is:inline></script>
    <script src="/scripts/api-client.js" is:inline></script>
    <script src="/scripts/auth-utils.js" is:inline></script>

    <!-- Add transition handler scripts -->
    <script src="/scripts/transition-handler.js" is:inline></script>
    <script src="/scripts/react-transition-handler.js" is:inline></script>

    <!-- Add progress bar for page transitions -->
    <script src="/scripts/progress-bar.js" is:inline></script>

    <script is:inline>
      // Initialize on first page load
      document.addEventListener("DOMContentLoaded", () => {
        // Initialize utilities
        if (window.UtilsInitializer) {
          window.UtilsInitializer.initAllUtils().then(({ cartUtils }) => {
            if (cartUtils) {
              cartUtils.updateCartBadge();
            }
          });
        } else if (window.CartUtils) {
          window.CartUtils.updateCartBadge();
        }

        // Add scroll behavior for header
        const header = document.getElementById("app-header");
        if (header) {
          let lastScrollTop = 0;

          window.addEventListener("scroll", () => {
            const scrollTop =
              window.pageYOffset || document.documentElement.scrollTop;

            // Add shadow when scrolling down
            if (scrollTop > 10) {
              header.classList.add("shadow-sm");
            } else {
              header.classList.remove("shadow-sm");
            }

            lastScrollTop = scrollTop;
          });
        }
      });

      // Also initialize on Astro page transitions
      document.addEventListener("astro:page-load", () => {
        if (window.UtilsInitializer) {
          window.UtilsInitializer.initAllUtils().then(({ cartUtils }) => {
            if (cartUtils) {
              cartUtils.updateCartBadge();
            }
          });
        } else if (window.CartUtils) {
          window.CartUtils.updateCartBadge();
        }
      });
    </script>
  </body>
</html>
