# Sreekar Publishers Database Schema

This directory contains the complete D1 SQLite database schema for Sreekar Publishers' educational materials catalog system.

## Overview

The database is designed specifically for an educational publisher specializing in materials for students in grades 6-10, supporting both Telugu and English medium instruction.

## Schema Files

1. **`sreekar_publishers_schema.sql`** - Complete database schema with tables, indexes, views, and triggers
2. **`sreekar_publishers_initial_data.sql`** - Initial reference data and sample books

## Database Structure

### Core Reference Tables

#### `grade_levels`
Stores grade levels from 6th to 10th grade.
- `grade_number` (6-10)
- `grade_name` (e.g., "6th Grade")
- `display_order` for UI sorting

#### `medium_types`
Language mediums for instruction.
- `medium_code` (TEL, ENG)
- `medium_name` (Telugu Medium, English Medium)

#### `subjects`
Academic subjects/categories.
- Core subjects: Mathematics, Science, Social Studies
- Language subjects: Telugu, English, Hindi
- Additional: Environmental Studies, Computer Science, Arts, PE
- Each subject has icon and color for UI

#### `book_series`
Collections/series of books.
- Main Series, Study Guides, Practice Books, Exam Preparation
- Workbooks, Reference, Activity Books, Assessment

### Main Entity Tables

#### `books`
Central table storing all educational materials.

**Key Fields:**
- **Basic Info**: title, subtitle, description, ISBN, URL slug
- **Classification**: subject_id, grade_level_id, medium_type_id, series_id
- **Pricing**: price, old_price, cost_price
- **Physical**: page_count, weight_grams, dimensions, binding_type
- **Status Flags**: is_featured, is_new_release, is_bestseller, is_on_sale, is_available
- **Stock**: stock_quantity, low_stock_threshold
- **SEO**: meta_title, meta_description, keywords
- **Publication**: publication_date, edition_number

### Association Tables

#### `tags` & `book_tags`
Flexible tagging system for books.
- Academic level tags: Beginner, Intermediate, Advanced
- Content type tags: Textbook, Workbook, Reference, Guide
- Exam preparation tags: Board Exam, Competitive Exam, Practice Tests
- Learning style tags: Visual Learning, Problem Solving, Conceptual, Practical
- Special features: Illustrated, Bilingual, Updated Syllabus, Previous Years

## Database Views

### `v_books_complete`
Complete book information with all related data joined.
- All book fields with subject, grade, medium, and series names
- Calculated fields like is_low_stock
- Ready for display in UI

### `v_books_by_grade_medium`
Statistical view showing book distribution.
- Count of books by grade, medium, and subject
- Average, minimum, and maximum prices
- Useful for analytics and reporting

### `v_stock_status`
Stock management view.
- Books categorized as: Out of Stock, Low Stock, In Stock
- Sorted by urgency for inventory management

## Database Triggers

### Automatic Timestamp Updates
- `tr_books_updated_at`: Updates `updated_at` field when books are modified

## Indexes for Performance

### Primary Lookup Indexes
- Combined indexes for common queries: subject + grade + medium
- URL slug and ISBN for unique lookups

### Status and Feature Indexes
- Partial indexes for featured, new release, bestseller, on sale books
- Available books index

### Sorting Indexes
- Price, rating, publication date, creation date
- Stock quantity and low stock detection

## Usage Examples

### Common Queries

```sql
-- Get all 7th grade Telugu medium mathematics books
SELECT * FROM v_books_complete
WHERE grade_number = 7
  AND medium_code = 'TEL'
  AND subject_code = 'MATH'
  AND is_available = 1;

-- Get featured books across all grades
SELECT * FROM v_books_complete
WHERE is_featured = 1
  AND is_available = 1
ORDER BY created_at DESC;

-- Get low stock books
SELECT * FROM v_stock_status
WHERE stock_status IN ('Out of Stock', 'Low Stock');

-- Get books by series
SELECT * FROM v_books_complete
WHERE series_name = 'Sreekar Main Series'
ORDER BY grade_number, subject_name;

-- Get all textbooks with visual learning approach
SELECT DISTINCT b.* FROM v_books_complete b
JOIN book_tags bt ON b.id = bt.book_id
JOIN tags t ON bt.tag_id = t.id
WHERE t.tag_slug IN ('textbook', 'visual-learning')
  AND b.is_available = 1;
```

## Installation

1. Execute `sreekar_publishers_schema.sql` to create the database structure
2. Execute `sreekar_publishers_initial_data.sql` to populate reference data and sample books

## Data Integrity

The schema includes:
- Foreign key constraints to maintain referential integrity
- Check constraints for valid data ranges (grades 6-10, ratings 1-5, etc.)
- Unique constraints for ISBNs, URL slugs, and codes
- NOT NULL constraints for required fields

## Scalability Considerations

- Indexes are optimized for common query patterns
- Views provide pre-joined data for complex queries
- Triggers maintain data consistency automatically
- Flexible tagging system allows for future categorization needs

## Educational Domain Features

- Grade-specific organization (6th-10th)
- Multi-language support (Telugu/English medium)
- Subject-based categorization
- Series/collection management
- Author attribution system
- Review and rating system
- Stock management for physical books
- SEO optimization for online catalog
