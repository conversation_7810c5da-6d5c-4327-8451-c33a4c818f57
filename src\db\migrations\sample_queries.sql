-- Sample Queries for Sreekar Publishers Database
-- Demonstrates common use cases and query patterns

-- ============================================================================
-- BASIC CATALOG QUERIES
-- ============================================================================

-- 1. Get all books for a specific grade and medium
-- Example: 8th grade Telugu medium books
SELECT 
    title,
    subject_name,
    price,
    stock_quantity,
    is_available
FROM v_books_complete 
WHERE grade_number = 8 
  AND medium_code = 'TEL'
  AND is_available = 1
ORDER BY subject_name, title;

-- 2. Get books by subject across all grades
-- Example: All Mathematics books
SELECT 
    grade_name,
    medium_name,
    title,
    price,
    average_rating,
    total_reviews
FROM v_books_complete 
WHERE subject_code = 'MATH'
  AND is_available = 1
ORDER BY grade_number, medium_code, title;

-- 3. Featured books for homepage
SELECT 
    title,
    subtitle,
    subject_name,
    grade_name,
    medium_name,
    price,
    old_price,
    primary_image,
    url_slug
FROM v_books_complete 
WHERE is_featured = 1 
  AND is_available = 1
ORDER BY created_at DESC
LIMIT 6;

-- ============================================================================
-- SEARCH AND FILTERING QUERIES
-- ============================================================================

-- 4. Search books by title or description
-- Example: Search for "mathematics" or "math"
SELECT 
    title,
    description,
    subject_name,
    grade_name,
    medium_name,
    price
FROM v_books_complete 
WHERE (title LIKE '%mathematics%' OR description LIKE '%mathematics%'
       OR title LIKE '%math%' OR description LIKE '%math%')
  AND is_available = 1
ORDER BY 
    CASE WHEN title LIKE '%mathematics%' THEN 1 ELSE 2 END,
    grade_number;

-- 5. Filter books by price range
-- Example: Books between ₹200 and ₹400
SELECT 
    title,
    subject_name,
    grade_name,
    medium_name,
    price,
    stock_quantity
FROM v_books_complete 
WHERE price BETWEEN 200 AND 400
  AND is_available = 1
ORDER BY price;

-- 6. Books with high ratings
-- Example: Books with 4+ star ratings
SELECT 
    title,
    subject_name,
    grade_name,
    average_rating,
    total_reviews,
    price
FROM v_books_complete 
WHERE average_rating >= 4.0 
  AND total_reviews >= 5
  AND is_available = 1
ORDER BY average_rating DESC, total_reviews DESC;

-- ============================================================================
-- INVENTORY AND STOCK MANAGEMENT
-- ============================================================================

-- 7. Low stock alert
SELECT 
    title,
    subject_name,
    grade_name,
    stock_quantity,
    low_stock_threshold,
    stock_status
FROM v_stock_status 
WHERE stock_status IN ('Out of Stock', 'Low Stock')
ORDER BY 
    CASE stock_status 
        WHEN 'Out of Stock' THEN 1 
        WHEN 'Low Stock' THEN 2 
    END,
    stock_quantity;

-- 8. Bestsellers by stock movement (assuming we track sales)
-- Note: This would require a sales/orders table for real implementation
SELECT 
    title,
    subject_name,
    grade_name,
    medium_name,
    stock_quantity,
    is_bestseller
FROM v_books_complete 
WHERE is_bestseller = 1
  AND is_available = 1
ORDER BY subject_name, grade_number;

-- ============================================================================
-- ANALYTICS AND REPORTING QUERIES
-- ============================================================================

-- 9. Books count by grade and medium
SELECT 
    grade_name,
    medium_name,
    book_count,
    avg_price,
    min_price,
    max_price
FROM v_books_by_grade_medium
ORDER BY grade_number, medium_code;

-- 10. Subject-wise book distribution
SELECT 
    s.subject_name,
    COUNT(b.id) as total_books,
    COUNT(CASE WHEN b.is_available = 1 THEN 1 END) as available_books,
    AVG(b.price) as avg_price,
    SUM(b.stock_quantity) as total_stock
FROM subjects s
LEFT JOIN books b ON s.id = b.subject_id
GROUP BY s.id, s.subject_name
ORDER BY s.display_order;

-- 11. Series performance
SELECT 
    bs.series_name,
    COUNT(b.id) as book_count,
    AVG(b.price) as avg_price,
    AVG(b.average_rating) as avg_rating,
    SUM(b.total_reviews) as total_reviews
FROM book_series bs
LEFT JOIN books b ON bs.id = b.series_id
WHERE b.is_available = 1
GROUP BY bs.id, bs.series_name
ORDER BY book_count DESC;

-- ============================================================================
-- ADVANCED QUERIES WITH TAGS
-- ============================================================================

-- 12. Books with specific learning approach
-- Example: Visual learning books
SELECT DISTINCT
    b.title,
    b.subject_name,
    b.grade_name,
    b.medium_name,
    b.price,
    GROUP_CONCAT(t.tag_name, ', ') as tags
FROM v_books_complete b
JOIN book_tags bt ON b.id = bt.book_id
JOIN tags t ON bt.tag_id = t.id
WHERE t.tag_slug = 'visual-learning'
  AND b.is_available = 1
GROUP BY b.id
ORDER BY b.grade_number, b.subject_name;

-- 13. Exam preparation materials
SELECT DISTINCT
    b.title,
    b.subject_name,
    b.grade_name,
    b.medium_name,
    b.price,
    b.average_rating
FROM v_books_complete b
JOIN book_tags bt ON b.id = bt.book_id
JOIN tags t ON bt.tag_id = t.id
WHERE t.tag_slug IN ('board-exam', 'competitive-exam', 'practice-tests')
  AND b.is_available = 1
ORDER BY b.grade_number, b.subject_name;

-- ============================================================================
-- AUTHOR AND PUBLICATION QUERIES
-- ============================================================================

-- 14. Books by author
-- Example: Books by Dr. Ramesh Kumar
SELECT 
    b.title,
    b.subject_name,
    b.grade_name,
    b.medium_name,
    ba.author_role,
    b.publication_date,
    b.edition_number
FROM v_books_complete b
JOIN book_authors ba ON b.id = ba.book_id
JOIN authors a ON ba.author_id = a.id
WHERE a.author_name = 'Dr. Ramesh Kumar'
  AND b.is_available = 1
ORDER BY b.publication_date DESC;

-- 15. Recent publications
-- Books published in the last year
SELECT 
    title,
    subject_name,
    grade_name,
    medium_name,
    publication_date,
    edition_number,
    is_new_release
FROM v_books_complete 
WHERE publication_date >= date('now', '-1 year')
  AND is_available = 1
ORDER BY publication_date DESC;

-- ============================================================================
-- CUSTOMER-FACING QUERIES
-- ============================================================================

-- 16. Related books (same subject, different grade)
-- Example: For a 7th grade math book, show 6th and 8th grade math books
WITH current_book AS (
    SELECT subject_id, grade_level_id, medium_type_id 
    FROM books 
    WHERE url_slug = 'mathematics-textbook-grade-7-english'
)
SELECT 
    b.title,
    b.grade_name,
    b.price,
    b.average_rating,
    b.url_slug
FROM v_books_complete b, current_book cb
WHERE b.subject_id = cb.subject_id
  AND b.medium_type_id = cb.medium_type_id
  AND b.grade_level_id != cb.grade_level_id
  AND b.is_available = 1
ORDER BY ABS(b.grade_number - (SELECT grade_number FROM grade_levels WHERE id = cb.grade_level_id))
LIMIT 4;

-- 17. Books in same series
-- Example: Other books in the same series
SELECT 
    title,
    subject_name,
    grade_name,
    medium_name,
    price,
    url_slug
FROM v_books_complete 
WHERE series_name = 'Sreekar Main Series'
  AND is_available = 1
ORDER BY grade_number, subject_name;

-- ============================================================================
-- ADMIN DASHBOARD QUERIES
-- ============================================================================

-- 18. Dashboard summary statistics
SELECT 
    'Total Books' as metric,
    COUNT(*) as value
FROM books
UNION ALL
SELECT 
    'Available Books',
    COUNT(*)
FROM books WHERE is_available = 1
UNION ALL
SELECT 
    'Featured Books',
    COUNT(*)
FROM books WHERE is_featured = 1 AND is_available = 1
UNION ALL
SELECT 
    'Low Stock Books',
    COUNT(*)
FROM books WHERE stock_quantity <= low_stock_threshold
UNION ALL
SELECT 
    'Out of Stock Books',
    COUNT(*)
FROM books WHERE stock_quantity = 0;

-- 19. Recent reviews requiring approval
SELECT 
    br.id,
    br.reviewer_name,
    br.rating,
    br.review_title,
    br.review_text,
    b.title as book_title,
    br.created_at
FROM book_reviews br
JOIN books b ON br.book_id = b.id
WHERE br.is_approved = 0
ORDER BY br.created_at DESC
LIMIT 10;

-- 20. Top-rated books by subject
SELECT 
    subject_name,
    title,
    average_rating,
    total_reviews,
    price
FROM (
    SELECT 
        *,
        ROW_NUMBER() OVER (PARTITION BY subject_name ORDER BY average_rating DESC, total_reviews DESC) as rn
    FROM v_books_complete 
    WHERE is_available = 1 
      AND total_reviews >= 3
) ranked
WHERE rn <= 3
ORDER BY subject_name, average_rating DESC;
